# Introduction to Drops.Relation

Drops.Relation is a high-level API for defining database relations with automatic schema inference and composable queries. It provides a convenient abstraction over Ecto that automatically introspects database tables, generates Ecto schemas, and offers a powerful query composition system.

## Core Philosophy

Drops.Relation bridges the gap between database schema introspection and application-level data modeling. Instead of manually defining Ecto schemas, you can automatically infer them from your database structure while still maintaining the flexibility to customize and extend them as needed.

## Main Components

A Drops.Relation module consists of several interconnected components that work together to provide a complete data access layer:

### 1. Relation Module
The main module that serves as the entry point for all database operations. It provides familiar Ecto.Repo-style functions like `all()`, `get()`, `insert()`, `update()`, and `delete()`.

### 2. Schema
A metadata structure that describes the database table, including fields, types, primary keys, foreign keys, and indices. Schemas can be automatically inferred from the database or manually defined.

### 3. Ecto Schema
Dynamically generated Ecto schema modules that provide struct definitions and type information. These are created automatically based on the relation's schema metadata.

### 4. QueryBuilder
A dedicated module for custom query definitions using the `defquery` macro. It provides a clean way to define reusable, composable query functions.

### 5. Enumerable Protocol
Implementation that allows relations to be used with `Enum` functions, enabling functional programming patterns for data processing.

### 6. Queryable Protocol
Integration with Ecto's query system, allowing relations to be used in Ecto queries and providing composable query operations like `restrict()` and `order()`.

## Basic Usage

Here's a simple example of defining a Users relation:

```elixir
defmodule MyApp.Users do
  use Drops.Relation, otp_app: :my_app

  schema("users", infer: true)
end
```

This automatically introspects the `users` table and provides a complete API:

```elixir
# Reading data
users = MyApp.Users.all()
user = MyApp.Users.get(1)
active_users = MyApp.Users.all_by(active: true)

# Writing data
{:ok, user} = MyApp.Users.insert(%{name: "Jane", email: "<EMAIL>"})
{:ok, updated_user} = MyApp.Users.update(user, %{active: true})

# Composable queries
query = MyApp.Users
        |> MyApp.Users.restrict(active: true)
        |> MyApp.Users.order(:name)

users = Enum.to_list(query)
```

## Architecture Overview

The following diagram illustrates how the components of a Users relation work together:

<!-- livebook:{"attrs":"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","chunks":null,"kind":"Elixir.KinoMermaid.ChartCell","livebook_object":"smart_cell"} -->

```mermaid
graph TB
    subgraph "MyApp.Users Relation Module"
        RM[Relation Module<br/>MyApp.Users]
        RM --> API[API Functions<br/>all(), get(), insert()<br/>update(), delete()]
        RM --> COMP[Composable Operations<br/>restrict(), order()]
    end

    subgraph "Schema Layer"
        SCHEMA[Schema<br/>Drops.Relation.Schema]
        SCHEMA --> FIELDS[Fields<br/>id: :integer<br/>name: :string<br/>email: :string<br/>active: :boolean]
        SCHEMA --> META[Metadata<br/>Primary Keys<br/>Foreign Keys<br/>Indices]
    end

    subgraph "Ecto Integration"
        ECTO[Ecto Schema<br/>MyApp.Users.User]
        ECTO --> STRUCT[Struct Definition<br/>%MyApp.Users.User{<br/>  id: nil,<br/>  name: nil,<br/>  email: nil,<br/>  active: nil<br/>}]
    end

    subgraph "Query System"
        QB[QueryBuilder<br/>MyApp.Users.QueryBuilder]
        QB --> CUSTOM[Custom Queries<br/>active()<br/>by_role(role)<br/>recent(days)]
    end

    subgraph "Protocol Implementations"
        ENUM[Enumerable Protocol<br/>Enum.to_list(relation)<br/>Enum.count(relation)]
        QUERY[Queryable Protocol<br/>Ecto Query Integration]
    end

    subgraph "Database"
        DB[(users table<br/>id, name, email, active<br/>inserted_at, updated_at)]
    end

    %% Relationships
    RM -.-> SCHEMA
    SCHEMA -.-> ECTO
    RM -.-> QB
    RM -.-> ENUM
    RM -.-> QUERY
    SCHEMA -.-> DB
    ECTO -.-> DB
    QB -.-> QUERY

    %% Styling
    classDef relationModule fill:#e1f5fe
    classDef schema fill:#f3e5f5
    classDef ecto fill:#e8f5e8
    classDef query fill:#fff3e0
    classDef protocol fill:#fce4ec
    classDef database fill:#f1f8e9

    class RM,API,COMP relationModule
    class SCHEMA,FIELDS,META schema
    class ECTO,STRUCT ecto
    class QB,CUSTOM query
    class ENUM,QUERY protocol
    class DB database
```

## Component Details

### Relation Module (MyApp.Users)
The main interface that provides:
- **API Functions**: Standard CRUD operations (`all()`, `get()`, `insert()`, `update()`, `delete()`)
- **Composable Operations**: Query building functions (`restrict()`, `order()`)
- **Schema Access**: Functions to access schema metadata (`schema()`, `schema(field)`)

### Schema Layer
Contains the metadata about your database table:
- **Fields**: Type information for each column (`:integer`, `:string`, `:boolean`, etc.)
- **Metadata**: Primary keys, foreign keys, indices, and other database constraints
- **Access**: Supports both `schema.field_name` and `schema[:field_name]` syntax

### Ecto Integration
Automatically generates Ecto schema modules:
- **Struct Definition**: Creates typed structs for your data
- **Type Safety**: Provides compile-time type checking
- **Ecto Compatibility**: Works seamlessly with existing Ecto code

### Query System
Enables custom query definitions:
- **QueryBuilder Module**: Dedicated module for query functions
- **Custom Queries**: Define reusable query functions with `defquery`
- **Composition**: All queries are composable with built-in operations

### Protocol Implementations
Makes relations work with Elixir's standard protocols:
- **Enumerable**: Use `Enum` functions directly on relations
- **Queryable**: Integration with Ecto's query system

## Practical Examples

### Schema Inference
```elixir
defmodule MyApp.Users do
  use Drops.Relation, otp_app: :my_app

  # Automatically infer schema from database
  schema("users", infer: true)
end

# Access schema information
schema = MyApp.Users.schema()
schema.source        # => :users
schema.fields        # => [%Field{name: :id, type: :integer}, ...]

# Access specific fields
email_field = schema[:email]
email_field.type     # => :string
```

### Custom Queries
```elixir
defmodule MyApp.Users do
  use Drops.Relation, otp_app: :my_app

  schema("users", infer: true)

  defquery active() do
    from(u in relation(), where: u.active == true)
  end

  defquery by_role(role) when is_binary(role) do
    from(u in relation(), where: u.role == ^role)
  end

  defquery recent(days \\ 7) do
    cutoff = DateTime.utc_now() |> DateTime.add(-days, :day)
    from(u in relation(), where: u.inserted_at >= ^cutoff)
  end
end

# Use custom queries
active_users = MyApp.Users.active() |> MyApp.Users.all()
admin_users = MyApp.Users.by_role("admin") |> MyApp.Users.all()
recent_users = MyApp.Users.recent(30) |> MyApp.Users.all()
```

### Query Composition
```elixir
# Chain operations together
query = MyApp.Users
        |> MyApp.Users.restrict(active: true)
        |> MyApp.Users.order(:name)
        |> MyApp.Users.active()  # Custom query

# Use with Enum functions
users = Enum.to_list(query)
count = Enum.count(query)
names = Enum.map(query, & &1.name)
```

### Manual Schema Definition
```elixir
defmodule MyApp.Users do
  use Drops.Relation, otp_app: :my_app

  schema("users") do
    field(:name, :string)
    field(:email, :string)
    field(:active, :boolean, default: true)
    field(:role, :string, default: "member")

    timestamps()
  end
end
```

### Mixed Approach (Inference + Custom Fields)
```elixir
defmodule MyApp.Users do
  use Drops.Relation, otp_app: :my_app

  schema("users", infer: true) do
    # Add virtual fields
    field(:full_name, :string, virtual: true)
    field(:display_name, :string, virtual: true)

    # Override defaults
    field(:role, :string, default: "member")
  end
end
```

## Key Benefits

1. **Automatic Schema Inference**: No need to manually define schemas for existing tables
2. **Type Safety**: Generated Ecto schemas provide compile-time type checking
3. **Composable Queries**: Build complex queries by chaining simple operations
4. **Familiar API**: Uses the same patterns as Ecto.Repo
5. **Extensible**: Easy to add custom queries and operations
6. **Protocol Integration**: Works seamlessly with Enum and other Elixir protocols

## Next Steps

- **Configuration**: Set up your application configuration
- **Schema Definition**: Learn about manual vs. automatic schema inference
- **Query Building**: Explore composable query operations
- **Custom Queries**: Define reusable query functions with `defquery`
- **Views**: Create specialized relation views with custom schemas
- **Testing**: Write tests for your relation modules

For detailed information on each topic, refer to the specific documentation sections.
